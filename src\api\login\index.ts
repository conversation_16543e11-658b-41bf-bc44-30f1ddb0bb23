import request from '@/config/axios'
import { getRefreshToken } from '@/utils/auth'
import type { UserLoginVO } from './types'

export interface SmsCodeVO {
  mobile: string
  scene: number
}

export interface SmsLoginVO {
  mobile: string
  code: string
}

// // 刷新访问令牌
// export const refreshToken = () => {
//   return request.post({ url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken() })
// }

// 刷新访问令牌 - 独立服务
export const refreshToken = () => {
  return request.post({
    url:
      `/auth/refresh-token?clientId=${import.meta.env.VITE_APP_CLIENT_ID}&refreshToken=` +
      getRefreshToken()
  })
}

// 使用租户名，获得租户编号
export const getTenantIdByName = (name: string) => {
  return request.get({ url: '/system/tenant/get-id-by-name?name=' + name })
}

// // 登出
// export const loginOut = () => {
//   return request.post({ url: '/system/auth/logout' })
// }

// 登出 - 独立服务
export const loginOut = () => {
  return request.post({ url: '/auth/logout' })
}

// 获取用户权限信息-旧
// export const getInfo = () => {
//   return request.get({ url: '/system/auth/get-permission-info' })
// }

// 获取用户权限信息-新
export const getInfo = () => {
  return request.post({ url: '/system/auth/get-login-user-info' })
}

// 获取用户权限信息-租户改造新版
export const getUserInfo = () => {
  return request.post({
    url: `system/auth/get-user-info`
  })
}

// 获取特定应用用户权限信息-新
export const getAppInfo = (params?) => {
  return request.get({ url: '/system/auth/get-login-user-info', params })
}

// 路由-旧
export const getAsyncRoutes = () => {
  return request.get({ url: '/system/auth/list-menus' })
}

// 路由-路由菜单+权限按钮-新
export const getRouteAndPermission = () => {
  return request.post({
    url: '/system/auth/get-login-user-permission'
  })
}

// 路由-路由菜单+权限按钮-新-租户改造
export const getRouteAndPermissionRenovation = (params?) => {
  return request.post({
    url: '/system/auth/get-user-permission',
    params
  })
}

//获取登录验证码
export const sendSmsCode = (data: SmsCodeVO) => {
  return request.post({ url: '/system/auth/send-sms-code', data })
}

// 短信验证码登录
export const smsLogin = (data: SmsLoginVO) => {
  return request.post({ url: '/system/auth/sms-login', data })
}

// 社交授权的跳转
export const socialAuthRedirect = (type: number, redirectUri: string) => {
  return request.get({
    url: '/system/auth/social-auth-redirect?type=' + type + '&redirectUri=' + redirectUri
  })
}
// 获取验证图片以及 token
export const getCode = (data) => {
  return request.postOriginal({ url: 'system/captcha/get', data })
}

// 滑动或者点选验证
export const reqCheck = (data) => {
  return request.postOriginal({ url: 'system/captcha/check', data })
}

// 发送验证码
export const sendCode = (params) => {
  return request.get({ url: 'system/email/sendCode', params })
}

// 校验验证码
export const validateCode = (params) => {
  return request.get({ url: 'system/email/validateCode', params })
}

// // 校验验证码
// export const getVerify = (params?) => {
//   return request.get({ url: 'system/captcha/getVerify', params })
// }

// 校验验证码 - 独立服务
export const getVerify = (params?) => {
  return request.get({ url: '/auth/get-verify', params })
}

// 通过邮箱重置用户密码
export const updateMailpassword = (data) => {
  return request.postOriginal({ url: 'system/user/updateMailpassword', data })
}

// 重置用户密码
export const modifyUserPassword = (data, token) => {
  return request.putWithTokenOriginal({
    url: `/system/user/modifyUserPassword`,
    data,
    token
  })
}

// 重置用户密码-新
export const updatePwd = (data, token) => {
  return request.putWithTokenOriginal({
    url: `/system/user/update-pwd`,
    data,
    token
  })
}
// 登录 v1版本
export const login = (data: UserLoginVO) => {
  return request.post({ url: '/system/auth/login', data })
}

/**
 * Authentication login response value object.
 */
export type AuthLoginRespVO = {
  /**
   * 用户编号
   */
  userId: number

  /**
   * 过期时间
   */
  expires_in: Date

  /**
   * 主页地址
   * @optional
   */
  url?: string

  /**
   * 首次登录标志
   * @optional
   */
  fisrt: boolean

  /**
   * 是否为第一次登录
   * @optional
   */
  loginFirstTime?: boolean

  /**
   * 密码是否过期
   */
  expired?: boolean

  /**
   * 所属租户ID
   * @optional
   */
  tenantId: string

  /**
   * 访问令牌
   */
  accessToken?: string

  access_token?: string

  /**
   * 刷新令牌
   */
  refreshToken?: string

  refresh_token?: string
}

// /####################登录相关类型
export interface LoginVO extends SmsCodeVO {
  clientId: string
}
export type AuthV2LoginReqVO<T> = {
  /** 登录方式。 */

  loginMethod: number

  /**
   * 登录信息。
   *
   * 登录信息使用string类型而不是使用json对象（map），是因为考虑到登录信息可能需要加密传输。
   * 如果需要加密传输则直接对整个请求信息json字符串进行加密。
   *
   */
  payload: T
  /**加密秘钥 */
  sk: string
  /**图片验证码ID */
  imageVerificationCodeId?: string
  /**图片验证码。 */
  imageVerificationCode?: string
}

//登录基础类型
export type BaseLoginPayload = {
  clientId: string
}

/**
 * 邮箱验证码登录
 */
export type MailCodeLoginPayload = BaseLoginPayload & {
  mail: string
  code: string
}

/**
 * 邮箱密码登录
 */
export type MailLoginPayload = BaseLoginPayload & {
  mail: string
  password: string
}

/**
 * 社交平台登录
 */
export type SocialLoginPayload = BaseLoginPayload & {
  /** 社交平台类型。 */
  type: string
  /** OAuth2.0授权码。 */
  code: string
  /** 随机字符串。 */
  state: string
  /** 登录渠道。 */
  channel?: string
  /** 重定向地址 */
  redirectUri: string
}

/**
 * 手机密码登录
 */
export type TelLoginPayload = BaseLoginPayload & {
  /** 手机号 */
  tel: string
  /** 密码 */
  password: string
}

/**
 * 手机验证码登录
 */
export type TelSmsLoginPayload = BaseLoginPayload & {
  /** 手机号 */
  tel: string
  /** 短信验证码 */
  code: string
}
//账号密码登录
export type AuthLoginReqVO = {
  username: string
  password: string
  /** 验证码*/
  captchaVerification: string
  /** 验证码key*/
  captchaKey: string
  /** 社交平台的类型 SysUserSocialTypeEnum 枚举值"*/
  socialType?: string
  /** 授权码*/
  socialCode?: string
  /** state*/
  socialState?: string
  /** 所属clientId*/
  clientId: string
  /** 主账号（租户ID）。*/
  tenantId: string
}

export type UsernameLoginPayload = AuthLoginReqVO & {
  /** 租户 */
  tenantId: string
}

type AccessTokenResponse = {
  /**
   * 访问令牌
   * @example "tudou"
   */
  access_token: string

  /**
   * 刷新令牌
   * @example "nice"
   */
  refresh_token: string

  /**
   * 令牌类型
   * @example "bearer"
   */
  token_type: string

  /**
   * 过期时间,单位：秒
   * @example 42430
   */
  expires_in: number

  /**
   * 授权范围,如果多个授权范围，使用空格分隔
   * @example "user_info"
   */
  scope: string

  /**
   * 精简的登录信息
   */
  loginInfo: {
    /**
     * 是否首次登录
     */
    loginFirstTime: boolean

    /**
     * 密码是否过期
     */
    expired: boolean

    /**
     * 租户ID
     */
    tenantId: string

    /**
     * 用户ID
     */
    userId: string
  }
}

// // 改版登录
// export const v2login = <T>(data: AuthV2LoginReqVO<T>): Promise<AuthLoginRespVO> => {
//   return request.post({ url: '/system/auth/v2/login', data })
// }

// 改版登录 - 独立服务
export const v2login = <T>(data: AuthV2LoginReqVO<T>): Promise<AccessTokenResponse> => {
  return request.post({ url: '/auth/login', data })
}

// 发送验证码
export const createAndSendVerificationCode = (data: SmsLoginVO) => {
  return request.postOriginal({
    url: '/system/verification-code/create-and-send-verification-code',
    data
  })
}

// // 获取RSA加密秘钥
// export const getEncodeKey = (params?) => {
//   return request.get({ url: '/system/auth/v2/get-encode-key', params })
// }

// 获取RSA加密秘钥 - 独立服务
export const getEncodeKey = (params?) => {
  return request.get({ url: '/auth/get-encode-key', params })
}

// 获取用户认证信息
export const getGenerateAuthenticateShortlink = (params?) => {
  return request.get({ url: '/system/user/get-authentication-info', params })
}

// 认证
export const authenticate = (data: SmsLoginVO) => {
  return request.post({ url: '/system/user/authenticate', data })
}

// 获取认证验证码
export const createAndSendAuthenticateVerificationCode = (data: SmsLoginVO) => {
  return request.post({ url: '/system/user/create-and-send-authenticate-verification-code', data })
}
// //获取登录方式接口
// export const getLoginMethods = (): Promise<
//   {
//     name: string
//     code: number
//     children: { name: string; code: number }[]
//   }[]
// > => {
//   return request.get({ url: '/system/auth/v2/getLoginMethods' })
// }

//获取登录方式接口 - 独立服务
export const getLoginMethods = (): Promise<
  {
    name: string
    code: number
    children: { name: string; code: number }[]
  }[]
> => {
  return request.get({ url: '/auth/get-login-methods' })
}

// 获取登录用户信息 - 独立服务
export const getLoginUserInfo = (params?) => {
  return request.get({ url: '/auth/get-login-user-info', params })
}
