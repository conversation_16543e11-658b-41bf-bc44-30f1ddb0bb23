<!-- 重置密码,1.首次登录需要重置密码  2. 忘记密码后的重置（先忘记密码后重置）-->
<template>
  <el-form
    ref="formLogin"
    :model="loginData.loginForm"
    :rules="LoginRules"
    class="login-form"
    label-position="top"
    label-width="120px"
    size="large"
  >
    <el-row style="maring-left: -10px; maring-right: -10px">
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item>
          <h2 class="text-2xl font-bold text-center xl:text-3xl enter-x xl:text-center">
            {{
              firstLoginObj?.loginInfo.expired == true
                ? t('profile.info.resetPwd')
                : t('login.resetPwd')
            }}
          </h2>
        </el-form-item>
      </el-col>
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item prop="password">
          <span class="text-[var(--client-color-primary-light-3)] mb-16px">{{
            firstLoginObj?.loginInfo.expired == true ? t('login.expiredTip') : ''
          }}</span>
          <el-input
            v-model="loginData.loginForm.password"
            :placeholder="t('login.pwdSet')"
            :prefix-icon="iconLock"
            show-password
            type="password"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item prop="newPassword">
          <el-input
            v-model="loginData.loginForm.newPassword"
            :placeholder="t('login.confirmPwd')"
            :prefix-icon="iconLock"
            show-password
            type="password"
            @keyup.enter="getCode()"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24" style="padding-left: 10px; padding-right: 10px">
        <el-form-item>
          <GoldButton
            :loading="resetLoading"
            :title="t('common.ok')"
            class="w-[100%]"
            type="primary"
            @click="getCode()"
          />
        </el-form-item>
      </el-col>
      <Verify
        ref="verify"
        :captchaType="captchaType"
        :imgSize="{ width: '400px', height: '200px' }"
        mode="pop"
        @success="fn"
        v-if="loginData.captchaEnable !== 'false'"
      />
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ResetForm'
})

// ========== 表单模块 ==========
// 表单相关导入
import { useFormValid } from './useLogin'
import { useIcon } from '@/hooks/web/useIcon'

// 表单状态和方法
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const iconLock = useIcon({ icon: 'ep:lock' })
const resetLoading = ref(false)

// 表单数据
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    username: '',
    password: '',
    captchaVerification: '',
    rememberMe: false,
    newPassword: ''
  }
})

// 表单校验规则
const equalToPassword = (_rule, value, callback) => {
  if (!value) {
    callback(t('sys.login.passwordPlaceholder'))
  } else if (loginData.loginForm.password !== value) {
    callback(new Error(t('profile.password.diffPwd')))
  } else {
    callback()
  }
}

const validPassword = (_rule, value, callback) => {
  let reg = !/^[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(value)
  if (!value) {
    callback(t('sys.login.passwordPlaceholder'))
  } else if (reg) {
    callback(new Error(t('login.pwdCheck')))
  } else {
    callback()
  }
}

const LoginRules = {
  password: [required, { required: true, validator: validPassword, trigger: 'blur' }],
  newPassword: [required, { required: true, validator: equalToPassword, trigger: 'blur' }]
}

// ========== 验证码模块 ==========
// 验证码相关导入
import { LOGIN_TYPE } from './useLogin'

// 验证码状态和方法
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字

const getCode = async () => {
  const data = await validForm()
  if (!data) return

  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await fn()
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    verify.value.show()
  }
}

// ========== 密码重置模块 ==========
// 密码重置相关导入
import { updateMailpassword, updatePwd } from '@/api/login/index'
import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import { useSsoStore } from '@/store/modules/sso'

// Store 初始化
const permissionStore = usePermissionStore()
const ssoStore = useSsoStore()
const { t } = useI18n()

// 组件通信
const props = defineProps(['code', 'email', 'loginType'])
const emit = defineEmits(['changeFormType'])

// 路由相关
const { currentRoute, push } = useRouter()
const { query } = useRoute()
const redirect = ref<string>('')

const firstLoginObj = ref()

// 监听路由变化
watch(
  () => currentRoute.value,
  (route) => {
    redirect.value = route?.query?.redirect as string
  },
  { immediate: true }
)

watch(
  () => props.loginType,
  () => {
    firstLoginObj.value = sessionStorage.getItem('firstLoginInfo')
      ? JSON.parse(sessionStorage.getItem('firstLoginInfo') as string)
      : undefined
  }
)

// 密码重置方法
const fn = () => {
  return sessionStorage.getItem('firstLoginInfo') ? resetPwdLogin() : returnLogin()
}

// 忘记密码重置处理
const returnLogin = async () => {
  const obj = {
    code: props.code,
    password: loginData.loginForm.password,
    email: props.email
  }
  resetLoading.value = true
  try {
    const res = await updateMailpassword(obj)
    if (res.code !== 0) return
    ElMessage.success(t('common.updateSuccess'))
    emit('changeFormType', LOGIN_TYPE.LOGIN)
  } finally {
    resetLoading.value = false
  }
}

// const firstLoginObj = computed(() => {
//   return sessionStorage.getItem('firstLoginInfo')
//     ? JSON.parse(sessionStorage.getItem('firstLoginInfo') as string)
//     : undefined
// })

// 首次登录重置处理
const resetPwdLogin = async () => {
  const firstLoginInfo = JSON.parse(sessionStorage.getItem('firstLoginInfo') as string)
  const obj = { pwd: loginData.loginForm.password }

  resetLoading.value = true
  try {
    const res = await updatePwd(obj, firstLoginInfo.access_token)
    if (res.code !== 0) return

    firstLoginInfo.fisrt = false
    sessionStorage.removeItem('firstLoginInfo')
    authUtil.setToken(firstLoginInfo)
    // 判断是否为SSO登录
    if (!redirect.value) redirect.value = '/HomePage'

    if (redirect.value.indexOf('sso') !== -1) {
      const clientId = redirect.value.split('client_id=')[1] as string
      const redirectUri = query.redirect_uri as string
      ssoStore.ssoAuth(clientId, redirectUri)
    } else {
      push({ path: redirect.value || permissionStore.addRouters[0].path })
    }
    ElMessage.success(t('common.updateSuccess'))
  } finally {
    resetLoading.value = false
  }
}

// ========== 组件暴露 ==========
defineExpose({ loginData })
</script>

<style lang="scss" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.login-code {
  width: 100%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    width: 100%;
    max-width: 100px;
    height: auto;
    vertical-align: middle;
  }
}
</style>
