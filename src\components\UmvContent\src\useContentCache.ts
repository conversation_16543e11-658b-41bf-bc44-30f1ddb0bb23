import { useCache } from '@/hooks/web/useCache'
import type { FooterConfig } from './types'

interface ContentCache {
  heightInitialized: boolean
  cachedHeight: number
  isResizeTriggered: boolean
}

// 基础缓存键
const BASE_CACHE_KEY = 'UMV_CONTENT_CACHE'

/**
 * 序列化 Footer 配置为字符串
 * 用于生成唯一的缓存键
 */
const serializeFooterConfig = (config?: FooterConfig): string => {
  if (!config) {
    return 'default'
  }

  const { enabled = false, height = 40, autoDetect = false } = config

  return `${enabled}-${height}-${autoDetect}`
}

/**
 * 生成基于配置的缓存键
 */
const getCacheKey = (footerConfig?: FooterConfig): string => {
  const configHash = serializeFooterConfig(footerConfig)
  return `${BASE_CACHE_KEY}_${configHash}`
}

/**
 * UmvContent 组件的缓存工具函数集合
 * 用于在不同组件实例间共享高度和状态信息
 * 支持基于 footer 配置的动态缓存键
 */
export function useContentCache() {
  // 使用 localStorage 缓存以在不同实例间共享
  const { wsCache } = useCache('localStorage')

  // 默认缓存对象
  const defaultCache: ContentCache = {
    heightInitialized: false,
    cachedHeight: 0,
    isResizeTriggered: false
  }

  /**
   * 获取缓存对象
   * @param footerConfig 当前的 footer 配置
   */
  const getCache = (footerConfig?: FooterConfig): ContentCache => {
    const cacheKey = getCacheKey(footerConfig)
    return wsCache.get(cacheKey) || defaultCache
  }

  /**
   * 更新缓存对象
   * @param cache 缓存数据
   * @param footerConfig 当前的 footer 配置
   */
  const setCache = (cache: ContentCache, footerConfig?: FooterConfig): void => {
    const cacheKey = getCacheKey(footerConfig)
    wsCache.set(cacheKey, cache)
  }

  /**
   * 更新缓存中的某个属性
   * @param key 要更新的属性键
   * @param value 新的属性值
   * @param footerConfig 当前的 footer 配置
   */
  const updateCache = <K extends keyof ContentCache>(
    key: K,
    value: ContentCache[K],
    footerConfig?: FooterConfig
  ): void => {
    const cache = getCache(footerConfig)
    cache[key] = value
    setCache(cache, footerConfig)
  }

  /**
   * 设置尺寸调整触发标志
   * @param triggered 是否触发重新计算
   * @param footerConfig 当前的 footer 配置
   */
  const setResizeTriggered = (triggered: boolean, footerConfig?: FooterConfig): void => {
    updateCache('isResizeTriggered', triggered, footerConfig)
  }

  /**
   * 设置高度初始化状态和缓存高度
   * @param height 计算得到的高度
   * @param footerConfig 当前的 footer 配置
   */
  const setCachedHeight = (height: number, footerConfig?: FooterConfig): void => {
    const cache = getCache(footerConfig)
    cache.heightInitialized = true
    cache.cachedHeight = height
    cache.isResizeTriggered = false
    setCache(cache, footerConfig)
  }

  /**
   * 重置指定配置的缓存
   * @param footerConfig 要重置的 footer 配置
   */
  const resetCache = (footerConfig?: FooterConfig): void => {
    setCache(
      {
        heightInitialized: false,
        cachedHeight: 0,
        isResizeTriggered: false
      },
      footerConfig
    )
  }

  /**
   * 清理所有 UmvContent 相关的缓存
   * 用于清理过期或无用的缓存项
   */
  const clearAllCaches = (): void => {
    try {
      const allKeys = Object.keys(localStorage)
      const umvContentKeys = allKeys.filter((key) => key.startsWith(BASE_CACHE_KEY))

      umvContentKeys.forEach((key) => {
        wsCache.delete(key)
      })

      console.log(`清理了 ${umvContentKeys.length} 个 UmvContent 缓存项`)
    } catch (error) {
      console.warn('清理 UmvContent 缓存失败:', error)
    }
  }

  /**
   * 获取指定配置的缓存键（用于调试）
   * @param footerConfig footer 配置
   */
  const getCurrentCacheKey = (footerConfig?: FooterConfig): string => {
    return getCacheKey(footerConfig)
  }

  return {
    getCache,
    setCache,
    updateCache,
    setResizeTriggered,
    setCachedHeight,
    resetCache,
    clearAllCaches,
    getCurrentCacheKey
  }
}
