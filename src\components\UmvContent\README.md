# UmvContent 组件

UmvContent 是一个智能的内容容器组件，提供自动高度计算、全屏功能和灵活的 Footer 高度处理。

## 功能特性

- 🎯 **自动高度计算**：智能计算容器高度，适应不同屏幕尺寸
- 🔄 **响应式设计**：支持窗口大小变化时自动调整
- 📱 **媒体查询支持**：可根据屏幕尺寸自动处理 Footer 显示
- ⚙️ **灵活配置**：支持多种 Footer 高度处理方案
- 🖥️ **全屏功能**：内置全屏切换功能
- 💾 **智能缓存**：缓存高度计算结果，提升性能

## 基础用法

```vue
<template>
  <UmvContent title="页面标题" message="提示信息">
    <!-- 搜索区域 -->
    <template #search>
      <div>搜索表单内容</div>
    </template>
    
    <!-- 主要内容 -->
    <div>页面主要内容</div>
    
    <!-- 分页区域 -->
    <template #pagination>
      <div>分页组件</div>
    </template>
  </UmvContent>
</template>

<script setup lang="ts">
import UmvContent from '@/components/UmvContent'
</script>
```

## Footer 配置

### FooterConfig 接口

```typescript
interface FooterConfig {
  // 是否启用 footer 高度减去功能
  enabled?: boolean
  // footer 高度值（像素）
  height?: number
  // 是否自动检测 footer 显示状态（基于媒体查询）
  autoDetect?: boolean
}
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | `boolean` | `false` | 是否启用 footer 高度减去功能 |
| `height` | `number` | `40` | footer 高度值（像素） |
| `autoDetect` | `boolean` | `false` | 是否自动检测 footer 显示状态 |

## 使用示例

### 1. 默认行为（不减去 Footer 高度）

```vue
<template>
  <UmvContent title="默认行为">
    <div>内容区域</div>
  </UmvContent>
</template>
```

### 2. 启用 Footer 高度减去（使用默认高度 50px）

```vue
<template>
  <UmvContent 
    title="启用 Footer"
    :footer-config="{ enabled: true }"
  >
    <div>内容区域</div>
  </UmvContent>
</template>
```

### 3. 自定义 Footer 高度

```vue
<template>
  <UmvContent 
    title="自定义高度"
    :footer-config="{ enabled: true, height: 60 }"
  >
    <div>内容区域</div>
  </UmvContent>
</template>
```

### 4. 自动检测 Footer 显示状态

```vue
<template>
  <UmvContent 
    title="自动检测"
    :footer-config="{ enabled: true, autoDetect: true }"
  >
    <div>内容区域</div>
  </UmvContent>
</template>

<script setup lang="ts">
// 当屏幕宽度 > 1366px 时，Footer 显示，会减去 Footer 高度
// 当屏幕宽度 <= 1366px 时，Footer 隐藏，不会减去 Footer 高度
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | `string` | `''` | 标题文本 |
| `message` | `string` | `''` | 提示信息 |
| `footerConfig` | `FooterConfig` | `{ enabled: false, height: 40, autoDetect: false }` | Footer 配置 |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| `default` | 默认插槽，主要内容区域 |
| `search` | 搜索区域插槽 |
| `pagination` | 分页区域插槽 |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `toggleFullscreen` | 切换全屏状态 | - |
| `setHeight` | 手动重新计算高度 | - |

## 注意事项

1. **媒体查询断点**：自动检测基于 `pc13_3` 断点（1366px），与项目中的媒体查询保持一致
2. **性能优化**：组件内置防抖机制，避免频繁的高度计算
3. **缓存机制**：高度计算结果会被缓存，提升页面切换性能
4. **向后兼容**：默认行为保持不变，不会影响现有代码

## 最佳实践

1. **响应式页面**：推荐使用 `autoDetect: true` 实现响应式 Footer 处理
2. **固定高度场景**：当 Footer 高度固定时，直接指定 `height` 值
3. **复杂逻辑**：使用 `customDetector` 实现复杂的显示逻辑
4. **性能考虑**：避免在 `customDetector` 中执行耗时操作
