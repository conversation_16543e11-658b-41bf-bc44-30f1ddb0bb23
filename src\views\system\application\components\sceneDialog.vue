<template>
  <el-dialog :title="'统计场景配置'" v-model="show" width="500">
    <el-form :model="formData" :label-width="80" ref="formRef" class="mt-16px">
      <el-form-item label="应用名称">
        <el-input v-model="formData.name" disabled />
      </el-form-item>
      <el-form-item label="场景类型">
        <el-select v-model="formData.statScene" multiple>
          <el-option
            v-for="(item, index) in senceTypeList"
            :key="index"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SceneDialog'
})

import { updateTenantPackageSence } from '@/api/system/application/index'
import { deepClone } from '@/utils/deep'

const formData = ref({
  name: undefined,
  statScene: undefined
})

const formLoading = ref(false)

// 场景类型
const senceTypeList = ref([
  {
    label: '业务统计',
    value: 'BUSINESS'
  },
  {
    label: '客户统计',
    value: 'CLIENT'
  }
])

const show = ref(false)

const close = () => {
  show.value = false
}

const rowData = ref()

const open = (_row) => {
  rowData.value = deepClone(_row)
  formData.value.name = _row.name

  if (_row.statScene) {
    formData.value.statScene = _row.statScene.split(',')
  } else {
    formData.value.statScene = undefined
  }
  show.value = true
}

const emit = defineEmits(['success'])

const message = useMessage()

const submitForm = async () => {
  try {
    formLoading.value = true
    if (formData.value.statScene) {
      rowData.value.statScene = formData.value.statScene.join(',')
    }
    await updateTenantPackageSence(rowData.value)
    message.success('操作成功')
    emit('success')
    close()
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>
