<template>
  <ElCard
    ref="contentWrapRef"
    :body-style="{ height: '100%', paddingBottom: '0px' }"
    :class="[
      prefixCls,
      'tableClass',
      isFullscreen ? 'content-wrap--fullscreen' : '',
      !isFullscreen && isAnimating ? 'content-wrap--exit-fullscreen' : ''
    ]"
    shadow="always"
  >
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-16px font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <Icon class="ml-5px" icon="bi:question-circle-fill" :size="14" />
        </ElTooltip>
      </div>
    </template>
    <div class="!h-full flex flex-col">
      <div v-if="$slots.search" class="!mb-0">
        <div v-show="showSearchForm">
          <slot name="search"> </slot>
        </div>
        <el-divider class="!m-0 !mb-3">
          <el-link
            v-if="showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = false)"
            :icon="CaretTop"
          >
            {{ t('common.shrink') }}
          </el-link>
          <el-link
            v-if="!showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = true)"
            :icon="CaretBottom"
          >
            {{ t('common.expand') }}
          </el-link>
        </el-divider>
      </div>
      <slot></slot>
      <div v-if="$slots.pagination" class="!mt-0">
        <slot name="pagination"> </slot>
      </div>
    </div>
  </ElCard>
</template>

<script setup lang="ts">
import { ElCard, ElTooltip } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import {
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  provide,
  watch,
  onActivated,
  type PropType
} from 'vue'
import { debounce } from 'lodash-es'
import { useContentCache } from './useContentCache'
import type { FooterConfig } from './types'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('umv-content')

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  // Footer 配置参数
  footerConfig: {
    type: Object as PropType<FooterConfig>,
    default: () => ({
      enabled: false,
      height: 40,
      autoDetect: false
    })
  }
})

// 使用缓存工具函数（无状态）
const { getCache, setCachedHeight, setResizeTriggered, getCurrentCacheKey } = useContentCache()

// 定义暴露给外部的方法
const emit = defineEmits(['fullscreen-change'])

// 全屏状态
const isFullscreen = ref(false)

// 动画状态
const isAnimating = ref(false)

// ContentWrap的DOM引用
const contentWrapRef = ref<any>(null)

// 内容高度
const contentHeight = ref(0)

// 高度验证状态
const heightValidityCallCount = ref(0)

// 切换全屏状态
const toggleFullscreen = () => {
  // 设置正在动画中
  isAnimating.value = true

  // 直接切换，不使用nextTick，避免延迟响应
  isFullscreen.value = !isFullscreen.value

  // 处理进入全屏时的文档滚动行为
  if (isFullscreen.value) {
    // 阻止body滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复body滚动
    document.body.style.overflow = ''
  }

  // 动画结束后重置状态并重新计算高度
  setTimeout(() => {
    isAnimating.value = false
    // 重置高度验证调用计数
    heightValidityCallCount.value = 0
    // 全屏状态变化后重新计算高度，确保footer配置生效
    setResizeTriggered(true, props.footerConfig)
    debouncedSetHeight()
  }, 300)

  // 触发全屏状态变更事件
  emit('fullscreen-change', isFullscreen.value)
}

// 使用依赖注入提供ContentWrap的全屏功能和状态
provide('contentWrapFullscreen', {
  toggleFullscreen,
  isFullscreen,
  // 添加一个方法让子组件可以监听全屏状态变化
  onFullscreenChange: (callback: (isFullscreen: boolean) => void) => {
    watch(
      isFullscreen,
      (newVal) => {
        callback(newVal)
      },
      { immediate: true }
    )
  }
})

// 为子组件提供内容高度
provide('contentHeight', {
  height: contentHeight,
  isFullscreen,
  getContentHeight: () => contentHeight.value
})

// 处理ESC键退出全屏
const handleEscKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    e.preventDefault() // 阻止默认行为
    e.stopPropagation() // 阻止事件冒泡
    toggleFullscreen()
  }
}

// 计算实际的 footer 高度
const getActualFooterHeight = (): number => {
  if (!props.footerConfig?.enabled) {
    return 0 // 默认不减去 footer 高度
  }

  // 如果启用自动检测
  if (props.footerConfig.autoDetect) {
    // 检测 footer 是否显示（基于媒体查询 pc13_3:hidden，即宽度 <= 1366px 时隐藏）
    const isFooterVisible = window.innerWidth > 1366
    if (!isFooterVisible) {
      return 0
    }
  }

  // 返回配置的高度或默认高度
  return props.footerConfig.height || 40
}

const setHeight = () => {
  if (!contentWrapRef.value || !contentWrapRef.value.$el) return

  // 如果正在动画中，延迟执行高度计算，避免在动画过程中计算导致异常
  if (isAnimating.value) {
    console.log('正在动画中，延迟高度计算')
    setTimeout(() => {
      if (!isAnimating.value) {
        setHeight()
      }
    }, 350) // 比动画时间稍长一点
    return
  }

  const tableRefEl = contentWrapRef.value.$el

  // 全屏状态下的特殊处理
  if (isFullscreen.value) {
    // 全屏状态下使用完整的窗口高度，不需要考虑footer
    const fullscreenHeight = window.innerHeight

    // 设置全屏高度
    tableRefEl.style.height = `${fullscreenHeight}px`
    contentHeight.value = fullscreenHeight
    return
  }

  // 获取当前配置对应的缓存
  const contentCache = getCache(props.footerConfig)

  // 如果高度已全局初始化，且不是窗口大小变化导致的调用，则使用缓存高度
  if (contentCache.heightInitialized && !contentCache.isResizeTriggered) {
    // 直接应用缓存的高度
    contentWrapRef.value.$el.style.height = `${contentCache.cachedHeight}px`
    // 更新内容高度引用
    contentHeight.value = contentCache.cachedHeight
    // 即使使用缓存的高度，也要检查高度是否合理
    nextTick(() => checkHeightValidity())
    return
  }

  nextTick(() => {
    try {
      // console.log('重新计算')

      const tableRefEl = contentWrapRef.value.$el
      const tableTop = tableRefEl.getBoundingClientRect().top
      const clientHeight = document?.querySelector('#app')?.clientHeight as number
      const footerHeight = getActualFooterHeight() // 使用新的 footer 高度计算函数

      if (clientHeight && tableTop) {
        const calculatedHeight = clientHeight - tableTop - footerHeight - 15
        if (calculatedHeight > 100) {
          // 确保计算的高度是合理的
          tableRefEl.style.height = `${calculatedHeight}px`

          // 更新内容高度引用
          contentHeight.value = calculatedHeight

          // 更新全局共享状态，传入当前配置
          setCachedHeight(calculatedHeight, props.footerConfig)

          // 检查高度设置后的有效性
          nextTick(() => checkHeightValidity())
        }
      }
    } catch (err) {
      console.error('UmvContent设置高度失败:', err)
    }
  })
}

// 计算动态阈值，根据footerConfig.enabled判断
const calculateDynamicThreshold = (): number => {
  const windowHeight = window.innerHeight
  const baseThreshold = Math.max(60, windowHeight * 0.05)

  // 如果启用了footer，使用较小的阈值；否则使用较大的阈值
  return props.footerConfig?.enabled ? baseThreshold : baseThreshold * 1.5
}

// 高度有效性检查函数
const checkHeightValidityInternal = () => {
  if (!contentWrapRef.value || !contentWrapRef.value.$el) return

  // 如果正在动画中，跳过高度验证，避免在动画过程中计算高度导致异常
  if (isAnimating.value) {
    console.log('正在动画中，跳过高度验证')
    return
  }

  // 检查连续调用次数，超过3次就停止
  if (heightValidityCallCount.value >= 3) {
    console.log('高度验证达到最大调用次数，停止检查')
    return
  }

  try {
    const tableRefEl = contentWrapRef.value.$el
    const rect = tableRefEl.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const bottomGap = windowHeight - rect.bottom
    const dynamicThreshold = calculateDynamicThreshold()

    // 如果底部间距过大，可能表示高度计算有问题，重新触发计算
    // 排除全屏状态，因为全屏状态下高度是固定的
    if (bottomGap > dynamicThreshold && !isFullscreen.value) {
      heightValidityCallCount.value++

      console.log('检测到底部间距异常，重新计算高度:', {
        bottomGap,
        threshold: dynamicThreshold,
        callCount: heightValidityCallCount.value
      })

      // 标记为需要重新计算，传入当前配置
      setResizeTriggered(true, props.footerConfig)

      // 延迟执行以避免可能的循环
      setTimeout(() => {
        debouncedSetHeight()
      }, 200)
    } else {
      // 验证通过，重置调用计数
      if (heightValidityCallCount.value > 0) {
        heightValidityCallCount.value = 0
      }
    }
  } catch (err) {
    console.error('检查高度有效性失败:', err)
    heightValidityCallCount.value++
  }
}

// 防抖版本的高度有效性检查函数
const checkHeightValidity = debounce(checkHeightValidityInternal, 300)

// 使用lodash的debounce函数
const debouncedSetHeight = debounce(setHeight, 100)

// 声明一个全局变量，用于标记窗口大小变化事件
declare global {
  interface Window {
    _isRealWindowResize?: boolean
  }
}

// 处理窗口大小变化
const handleContentWrapResize = () => {
  // 获取当前配置对应的缓存状态
  const cache = getCache(props.footerConfig)

  // 如果已经初始化了高度，则不再响应 content-wrap-resize 事件
  // 只响应浏览器窗口大小变化的 resize 事件
  if (cache.heightInitialized && !window._isRealWindowResize) {
    // console.log('已有缓存高度，忽略 content-wrap-resize 事件')
    return
  }

  setResizeTriggered(true, props.footerConfig)
  debouncedSetHeight()
}

// 处理真正的窗口大小变化
const handleWindowResize = () => {
  // 标记这是真正的窗口 resize 事件
  window._isRealWindowResize = true
  // 重置高度验证调用计数
  heightValidityCallCount.value = 0
  setResizeTriggered(true, props.footerConfig)
  debouncedSetHeight()
  // 延迟删除标记
  setTimeout(() => {
    window._isRealWindowResize = undefined
  }, 100)
}

// 监听 footerConfig 变化
watch(
  () => props.footerConfig,
  (newConfig) => {
    // 当 footer 配置变化时，重新计算高度
    // 由于缓存函数现在是无状态的，会自动使用新配置的缓存键
    setResizeTriggered(true, newConfig)
    debouncedSetHeight()

    console.log('Footer 配置已更新，新缓存键:', getCurrentCacheKey(newConfig))
  },
  { deep: true }
)

// 如果启用了自动检测，监听窗口大小变化以响应媒体查询
watch(
  () => props.footerConfig?.autoDetect,
  (autoDetect) => {
    if (autoDetect) {
      // 当启用自动检测时，确保窗口大小变化时重新计算
      setResizeTriggered(true)
      debouncedSetHeight()
    }
  }
)

onMounted(() => {
  debouncedSetHeight()
  // 添加ESC键监听 - 使用capture模式确保事件在捕获阶段被处理
  document.addEventListener('keydown', handleEscKeydown, { capture: true })
  // 添加窗口大小变化监听，使用特定的事件名称
  window.addEventListener('resize', handleWindowResize)
  // 监听自定义事件
  window.addEventListener('content-wrap-resize', handleContentWrapResize)

  // 初始化完成后额外检查一次高度有效性
  nextTick(() => {
    setTimeout(checkHeightValidity, 500)
  })
})

onActivated(() => {
  // 使用最新的缓存高度
  setHeight()
})

onUnmounted(() => {
  // 移除ESC键监听 - 确保使用相同的配置
  document.removeEventListener('keydown', handleEscKeydown, { capture: true })
  // 移除窗口大小变化监听，使用特定的事件名称
  window.removeEventListener('resize', handleWindowResize)
  // 移除自定义事件监听
  window.removeEventListener('content-wrap-resize', handleContentWrapResize)
})

//是否展示搜索
const showSearchForm = ref(true)

// 暴露方法给父组件
defineExpose({
  toggleFullscreen,
  isFullscreen,
  setHeight
})
</script>

<style scoped>
/* 全屏样式基础 */
.umv-content {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
}

/* 全屏样式 */
.content-wrap--fullscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  margin: 0;
  padding: 0;
  width: 100vw !important;
  overflow: auto;
  background-color: var(--el-bg-color);
  border-radius: 0 !important;
  animation: umv-content-fullscreen-in 0.3s ease-in-out forwards;
}

/* 退出全屏样式 */
.content-wrap--exit-fullscreen {
  animation: umv-content-fullscreen-out 0.3s ease-in-out forwards;
}

/* 进入全屏动画 */
@keyframes umv-content-fullscreen-in {
  from {
    opacity: 0.9;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 退出全屏动画 */
@keyframes umv-content-fullscreen-out {
  from {
    transform: scale(1.02);
    opacity: 0.95;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.content-wrap--fullscreen :deep(.el-card__body) {
  height: 100% !important;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 20px;
}

.content-wrap--fullscreen :deep(.el-card__header) {
  padding: 12px 20px;
}
</style>
