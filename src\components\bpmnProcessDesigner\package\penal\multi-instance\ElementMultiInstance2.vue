<template>
  <div class="panel-tab__content">
    <el-form label-width="90px">
      <el-form-item label="审批方式">
        <el-radio-group
          v-model="multiLoopType"
          @change="changeMultiLoopType"
          style="display: flex; flex-direction: column; align-items: flex-start"
        >
          <el-radio label="oneAgree">或签（一名审批人同意即可）</el-radio>
          <el-radio label="allAgree">会签（需所有审批人同意）</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ElementMultiInstance'
})

const props = defineProps({
  businessObject: Object,
  type: String
})
const prefix = inject('prefix')
const loopCharacteristics = ref('')

// #################审批方式👇👇👇👇👇👇#################

//会签/或签
let multiLoopType = ref('oneAgree')
//多实例条件枚举
enum CONDITION_TYPE {
  ALL_AGREE = '${nrOfCompletedInstances==nrOfInstances}',
  ONE_AGREE = '${nrOfCompletedInstances==1}'
}

const changeMultiLoopType = (val) => {
  console.log(val)

  //多实例条件
  let condition = val === 'allAgree' ? CONDITION_TYPE.ALL_AGREE : CONDITION_TYPE.ONE_AGREE //并行多重事件-完成条件

  multiLoopInstance.value = bpmnInstances().moddle.create('bpmn:MultiInstanceLoopCharacteristics', {
    collection: '${coll_userList}'
  })

  bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
    loopCharacteristics: toRaw(multiLoopInstance.value)
  })

  let completionCondition = null
  if (condition && condition.length) {
    completionCondition = bpmnInstances().moddle.create('bpmn:FormalExpression', {
      body: condition
    })
  }
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    {
      completionCondition
    }
  )
}

// 回显审批方式
watch(
  () => props.businessObject,
  (val) => {
    let condition = val?.loopCharacteristics?.completionCondition?.body
    if (condition === '' || condition === undefined) {
      // 默认设置为或签
      multiLoopType.value = 'oneAgree'
      // 初始化时如果没有设置审批方式，自动设置为或签
      nextTick(() => {
        changeMultiLoopType('oneAgree')
      })
      return
    }
    //TODO :可能 条件会因为条件中空格问题出bug
    multiLoopType.value = condition === CONDITION_TYPE.ALL_AGREE ? 'allAgree' : 'oneAgree'
  },
  { immediate: true }
)

// ####################审批方式👆👆👆👆👆👆###########################

//默认配置，用来覆盖原始不存在的选项，避免报错
const defaultLoopInstanceForm = ref({
  completionCondition: '',
  loopCardinality: '',
  extensionElements: [],
  asyncAfter: false,
  asyncBefore: false,
  exclusive: false
})
const loopInstanceForm = ref<any>({})
const bpmnElement = ref(null)
const multiLoopInstance = ref(null)
const bpmnInstances = () => (window as any)?.bpmnInstances

const getElementLoop = (businessObject) => {
  if (!businessObject.loopCharacteristics) {
    loopCharacteristics.value = 'ParallelMultiInstance'
    loopInstanceForm.value = {}
    // 如果没有设置审批方式，默认设置为或签
    nextTick(() => {
      changeMultiLoopType('oneAgree')
    })
    return
  }
  if (businessObject.loopCharacteristics.$type === 'bpmn:StandardLoopCharacteristics') {
    loopCharacteristics.value = 'ParallelMultiInstance'
    loopInstanceForm.value = {}
    // 如果是标准循环，也转换为或签
    nextTick(() => {
      changeMultiLoopType('oneAgree')
    })
    return
  }
  if (businessObject.loopCharacteristics.isSequential) {
    loopCharacteristics.value = 'SequentialMultiInstance'
  } else {
    loopCharacteristics.value = 'ParallelMultiInstance'
  }
  // 合并配置
  loopInstanceForm.value = {
    ...defaultLoopInstanceForm.value,
    ...businessObject.loopCharacteristics,
    completionCondition: businessObject.loopCharacteristics?.completionCondition?.body ?? '',
    loopCardinality: businessObject.loopCharacteristics?.loopCardinality?.body ?? ''
  }
  // 保留当前元素 businessObject 上的 loopCharacteristics 实例
  multiLoopInstance.value = bpmnInstances().bpmnElement.businessObject.loopCharacteristics
  // 更新表单
  if (
    businessObject.loopCharacteristics.extensionElements &&
    businessObject.loopCharacteristics.extensionElements.values &&
    businessObject.loopCharacteristics.extensionElements.values.length
  ) {
    loopInstanceForm.value['timeCycle'] =
      businessObject.loopCharacteristics.extensionElements.values[0].body
  }
}
const changeLoopCharacteristicsType = (type) => {
  // this.loopInstanceForm = { ...this.defaultLoopInstanceForm }; // 切换类型取消原表单配置
  // 取消多实例配置
  if (type === 'Null') {
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
      loopCharacteristics: null
    })
    return
  }
  // 配置循环
  if (type === 'StandardLoop') {
    const loopCharacteristicsObject = bpmnInstances().moddle.create(
      'bpmn:StandardLoopCharacteristics'
    )
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
      loopCharacteristics: loopCharacteristicsObject
    })
    multiLoopInstance.value = null
    return
  }
  // 时序
  if (type === 'SequentialMultiInstance') {
    multiLoopInstance.value = bpmnInstances().moddle.create(
      'bpmn:MultiInstanceLoopCharacteristics',
      { isSequential: true }
    )
  } else {
    multiLoopInstance.value = bpmnInstances().moddle.create(
      'bpmn:MultiInstanceLoopCharacteristics',
      { collection: '${coll_userList}' }
    )
  }
  bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
    loopCharacteristics: toRaw(multiLoopInstance.value)
  })
}
// 循环基数
const updateLoopCardinality = (cardinality) => {
  let loopCardinality = null
  if (cardinality && cardinality.length) {
    loopCardinality = bpmnInstances().moddle.create('bpmn:FormalExpression', {
      body: cardinality
    })
  }
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    {
      loopCardinality
    }
  )
}
// 完成条件
const updateLoopCondition = (condition) => {
  let completionCondition = null
  if (condition && condition.length) {
    completionCondition = bpmnInstances().moddle.create('bpmn:FormalExpression', {
      body: condition
    })
  }
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    {
      completionCondition
    }
  )
}
// 重试周期
const updateLoopTimeCycle = (timeCycle) => {
  const extensionElements = bpmnInstances().moddle.create('bpmn:ExtensionElements', {
    values: [
      bpmnInstances().moddle.create(`${prefix}:FailedJobRetryTimeCycle`, {
        body: timeCycle
      })
    ]
  })
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    {
      extensionElements
    }
  )
}
// 直接更新的基础信息
const updateLoopBase = () => {
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    {
      collection: loopInstanceForm.value.collection || null,
      elementVariable: loopInstanceForm.value.elementVariable || null
    }
  )
}
// 各异步状态
const updateLoopAsync = (key) => {
  const { asyncBefore, asyncAfter } = loopInstanceForm.value
  let asyncAttr = Object.create(null)
  if (!asyncBefore && !asyncAfter) {
    // this.$set(this.loopInstanceForm, "exclusive", false);
    loopInstanceForm.value['exclusive'] = false
    asyncAttr = { asyncBefore: false, asyncAfter: false, exclusive: false, extensionElements: null }
  } else {
    asyncAttr[key] = loopInstanceForm.value[key]
  }
  bpmnInstances().modeling.updateModdleProperties(
    toRaw(bpmnElement.value),
    multiLoopInstance.value,
    asyncAttr
  )
}

onBeforeUnmount(() => {
  multiLoopInstance.value = null
  bpmnElement.value = null
})

watch(
  () => props.businessObject,
  (val) => {
    bpmnElement.value = bpmnInstances().bpmnElement
    getElementLoop(val)
  },
  { immediate: true }
)
</script>
