<template>
  <el-dialog :title="titleMap[type]" v-model="show">
    <el-form :model="formData" :rules="formRules" :label-width="80" ref="formRef">
      <el-form-item label="业务端" prop="clientId" :style="{ width: '100%' }">
        <el-select
          v-model="formData.clientId"
          :style="{ width: '100%' }"
          placeholder="请选择业务端"
        >
          <el-option
            v-for="(item, index) in clientList"
            :key="index"
            :label="item.name + '(' + item.code + ')'"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入应用名称" />
      </el-form-item>

      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入编号" />
      </el-form-item>
      <el-form-item label="版本" prop="ver">
        <el-input v-model="formData.ver" placeholder="请输入版本" />
      </el-form-item>
      <el-form-item label="场景类型">
        <el-select v-model="formData.statScene" multiple>
          <el-option
            v-for="(item, index) in senceTypeList"
            :key="index"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="formData.description" placeholder="请输入描述" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'PackageOprateDialog'
})

import { addTenantPackage } from '@/api/system/application/index'
const show = ref(false)

const type = ref('add')

const titleMap = ref({
  add: '添加',
  edit: '编辑'
})

const formData = ref({
  clientId: undefined,
  ver: undefined,
  code: undefined,
  description: undefined,
  name: undefined,
  statScene: undefined
})

const formRules = ref({
  clientId: [{ required: true, trigger: ['blur', 'change'], message: '请选择业务端' }],
  ver: [{ required: true, trigger: ['blur', 'change'], message: '请输入版本' }],
  code: [{ required: true, trigger: ['blur', 'change'], message: '请输入描述' }],
  name: [{ required: true, trigger: ['blur', 'change'], message: '请输入应用名' }]
})

// 场景类型
const senceTypeList = ref([
  {
    label: '业务统计',
    value: 'BUSINESS'
  },
  {
    label: '客户统计',
    value: 'CLIENT'
  }
])

//获取客户端列表
import { useHook } from '../common/useHook'
const { clientList, getClientList } = useHook()

const close = () => {
  initData()
  show.value = false
}

const formLoading = ref(false)

const initData = () => {
  formData.value = {
    clientId: undefined,
    ver: undefined,
    code: undefined,
    description: undefined,
    statScene: undefined
  }
}

const open = (_type, _row?) => {
  initData()
  if (_row) {
    formData.value = _row
  }
  getClientList()
  type.value = _type
  show.value = true
}

const message = useMessage()

const emits = defineEmits(['success'])

const formRef = ref()

const submitForm = async () => {
  await formRef.value.validate()
  try {
    formLoading.value = true
    const fn = addTenantPackage
    const res = await fn({
      ...formData.value,
      statScene: formData.value.statScene ? formData.value.statScene.join(',') : undefined
    })
    message.success('操作成功')
    emits('success')
    close()
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>
