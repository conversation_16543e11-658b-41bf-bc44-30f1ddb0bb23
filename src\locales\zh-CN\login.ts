export default {
  welcome: '欢迎使用本系统',
  message: '开箱即用的中后台管理系统',
  tenantname: '租户名称',
  username: '用户名',
  password: '密码',
  code: '验证码',
  login: '登录',
  relogin: '重新登录',
  otherLogin: '其他登录方式',
  register: '注册',
  checkPassword: '确认密码',
  remember: '记住我',
  hasUser: '已有账号？去登录',
  forgetPassword: '忘记密码?',
  tenantNamePlaceholder: '请输入租户名称',
  usernamePlaceholder: '请输入用户名',
  passwordPlaceholder: '请输入密码',
  codePlaceholder: '请输入验证码',
  mobileTitle: '手机登录',
  mobileNumber: '手机号码',
  mobileNumberPlaceholder: '请输入手机号码',
  backLogin: '返回',
  getSmsCode: '获取验证码',
  btnMobile: '手机登录',
  btnQRCode: '二维码登录',
  qrcode: '扫描二维码登录',
  btnRegister: '注册',
  SmsSendMsg: '验证码已发送',
  forgetPwd: '忘记密码',
  // @ts-ignore
  emailPlaceholder: "请输入账号，如*******{'@'}goldpac.com",
  pleaseInput: '请输入',
  next: '下一步',
  resend: 's 重新发送',
  secdSuccess: '发送成功',
  account: '账号',
  sendCode: '发送验证码',
  pwdCheck: '请输入6-18位，数字、字母、符号（不含空格）的密码',
  findNone: '未找到该租户信息',
  loadingSystem: '正在加载系统中...',
  nowaySetting: '此方式未配置',
  reget: '秒后可重新获取',
  resetPwd: '重置密码',
  expiredTip: '您的密码已过期，必须修改以后才能使用',
  pwdSet: '设置6-18位，数字、字母、符号（不含空格）的密码',
  confirmPwd: '确认密码',
  thiredPartyGetAuth: '此第三方应用请求获得以下权限：',
  agreeAuth: '同意授权',
  authing: '授 权 中...',
  refuse: '拒绝',
  getYourInfo: '访问你的个人信息',
  changeYourInfo: '修改你的个人信息',
  sendCardService: 'DIY云发卡服务',
  introOne: '结合AI、3D、VR等技术，为银行提供卡面、卡号、卡等级、权益等定制服务',
  introTwo: '个性卡面自定义：个性拼图、美图精选、密码定制、上传照片',
  introThree: '专属靓号定制：亲子号、爱情号、纪念号、商旅靓号',
  introFour: '优质权益选择：优质权益、自由组合、套餐选择、灵活便宜',
  introFive: '费用分账报表：支持银行或者行业多级费用分账',
  introSix: '数据统计分析：用户数据分析，助力运营决策',
  introSeven: '图片审核：提供专业的图片审核服务',
  loginError: '登录失败',
  noClient: '该用户没有当前交互端的权限，请联系管理员!',
  noClientLoginOut: '该用户没有当前交互端的权限,是否退回登录页?',
  thirdplatformLogin: '第三方平台登录',
  thirdplatformLoginGoogle: '谷歌登录'
}
